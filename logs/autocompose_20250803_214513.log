2025-08-03 21:45:13 - autocompose.main - INFO - setup_logging:173 - Logging initialized - Level: INFO, File: logs/autocompose_20250803_214513.log
2025-08-03 21:45:13 - autocompose.system - INFO - log_system_info:191 - Python version: 3.9.7 | packaged by conda-forge | (default, Sep  2 2021, 17:58:34) 
[GCC 9.4.0]
2025-08-03 21:45:13 - autocompose.system - INFO - log_system_info:192 - Platform: Linux-3.10.0-957.el7.x86_64-x86_64-with-glibc2.17
2025-08-03 21:45:13 - autocompose.system - INFO - log_system_info:193 - Architecture: ('64bit', 'ELF')
2025-08-03 21:45:13 - autocompose.system - INFO - log_system_info:198 - PyTorch version: 2.5.1+cu121
2025-08-03 21:45:13 - autocompose.system - INFO - log_system_info:199 - CUDA available: True
2025-08-03 21:45:13 - autocompose.system - INFO - log_system_info:201 - CUDA version: 12.1
2025-08-03 21:45:13 - autocompose.system - INFO - log_system_info:202 - GPU count: 1
2025-08-03 21:45:13 - autocompose.system - INFO - log_system_info:205 - GPU 0: NVIDIA A800-SXM4-80GB
2025-08-03 21:45:13 - autocompose.system - INFO - log_system_info:213 - Total memory: 2015.3 GB
2025-08-03 21:45:13 - autocompose.system - INFO - log_system_info:214 - Available memory: 1909.2 GB
2025-08-03 21:45:13 - autocompose.main - INFO - __init__:68 - AutoComposeAgent initialized successfully
2025-08-03 21:45:13 - autocompose.main - INFO - process_image:132 - Processing image: input.png (size: (1133, 753))
2025-08-03 21:45:13 - autocompose.main - INFO - _timed_step:91 - Step 1: Analyzing image composition...
2025-08-03 21:45:13 - autocompose.main - INFO - __enter__:233 - Starting: Perception Analysis
2025-08-03 21:45:14 - autocompose.agents.perception - ERROR - analyze_image:70 - llama-vision analysis failed: CUDA error: CUBLAS_STATUS_NOT_SUPPORTED when calling `cublasLtMatmulAlgoGetHeuristic( ltHandle, computeDesc.descriptor(), Adesc.descriptor(), Bdesc.descriptor(), Cdesc.descriptor(), Cdesc.descriptor(), preference.descriptor(), 1, &heuristicResult, &returnedResult)`
2025-08-03 21:45:14 - performance - INFO - end_timer:58 - Operation 'perception analysis' completed in 0.434s
2025-08-03 21:45:14 - autocompose.main - INFO - __exit__:239 - Completed: Perception Analysis (0.436s)
2025-08-03 21:45:14 - autocompose.main - INFO - process_image:142 - Identified 0 composition issues: []
2025-08-03 21:45:14 - autocompose.main - INFO - process_image:143 - Generated 0 recommendations
2025-08-03 21:45:14 - autocompose.main - INFO - _timed_step:91 - Step 2: Generating adjustment plan...
2025-08-03 21:45:14 - autocompose.main - INFO - __enter__:233 - Starting: Plan Generation
2025-08-03 21:45:14 - autocompose.agents.planning - ERROR - _query_local_qwen:191 - Local model query failed: CUDA error: out of memory
CUDA kernel errors might be asynchronously reported at some other API call, so the stacktrace below might be incorrect.
For debugging consider passing CUDA_LAUNCH_BLOCKING=1
Compile with `TORCH_USE_CUDA_DSA` to enable device-side assertions.

2025-08-03 21:45:14 - performance - INFO - end_timer:58 - Operation 'plan generation' completed in 0.066s
2025-08-03 21:45:14 - autocompose.main - INFO - __exit__:239 - Completed: Plan Generation (0.066s)
2025-08-03 21:45:14 - autocompose.main - INFO - process_image:150 - Generated plan with 1 instructions
2025-08-03 21:45:14 - autocompose.main - INFO - process_image:152 - Instructions: ['Crop the image to improve composition using center framing']
2025-08-03 21:45:14 - autocompose.main - INFO - _timed_step:91 - Step 3: Executing adjustment plan...
2025-08-03 21:45:14 - autocompose.main - INFO - __enter__:233 - Starting: Plan Execution
2025-08-03 21:45:14 - autocompose.main - INFO - process_image:164 - Using Planning Chain execution with feedback control
2025-08-03 21:45:14 - autocompose.agents.restoration - INFO - execute_plan:275 - Planning Chain enabled, using enhanced execution with feedback control
2025-08-03 21:45:14 - autocompose.agents.controller - INFO - __init__:64 - Planning Chain Controller initialized:
2025-08-03 21:45:14 - autocompose.agents.controller - INFO - __init__:65 -   - Rollback threshold: 0.0
2025-08-03 21:45:14 - autocompose.agents.controller - INFO - __init__:66 -   - Max local rollbacks: 2
2025-08-03 21:45:14 - autocompose.agents.controller - INFO - __init__:67 -   - Max global rollbacks: 1
2025-08-03 21:45:14 - autocompose.agents.controller - INFO - start_execution:75 - Started execution of plan with 1 steps
2025-08-03 21:45:14 - autocompose.agents.restoration - INFO - _execute_plan_with_planning_chain:304 - Starting Planning Chain execution with 1 instructions
2025-08-03 21:45:14 - autocompose.agents.restoration - INFO - _execute_plan_with_planning_chain:305 - Initial aesthetic score: 0.584
2025-08-03 21:45:14 - autocompose.agents.restoration - INFO - _execute_plan_with_planning_chain:318 - Executing step 1: Crop the image to improve composition using center framing
2025-08-03 21:45:14 - autocompose.agents.controller - INFO - evaluate_step_result:102 - Evaluating step: 'Crop the image to improve composition using center...'
2025-08-03 21:45:14 - autocompose.agents.controller - INFO - evaluate_step_result:103 -   Success: False, Improvement: 0.000
2025-08-03 21:45:14 - autocompose.agents.restoration - INFO - _execute_plan_with_planning_chain:330 - Controller decision: local - Execution failed: No available model could process this instruction
2025-08-03 21:45:14 - autocompose.agents.controller - INFO - execute_rollback:218 - Executing local rollback (attempt 1)
2025-08-03 21:45:14 - autocompose.agents.planning - INFO - replan_local:553 - Starting local replan for failed step: Crop the image to improve composition using center framing
2025-08-03 21:45:14 - autocompose.agents.planning - INFO - replan_local:554 - Failure reason: Execution failed: No available model could process this instruction
2025-08-03 21:45:14 - autocompose.agents.planning - INFO - replan_local:555 - Performance impact: 0.000
2025-08-03 21:45:14 - autocompose.agents.planning - ERROR - _query_local_qwen:191 - Local model query failed: CUDA error: invalid argument
CUDA kernel errors might be asynchronously reported at some other API call, so the stacktrace below might be incorrect.
For debugging consider passing CUDA_LAUNCH_BLOCKING=1
Compile with `TORCH_USE_CUDA_DSA` to enable device-side assertions.

2025-08-03 21:45:14 - autocompose.agents.planning - ERROR - _create_alternative_instruction:470 - Error creating alternative instruction with LLM: Failed to extract valid instruction from LLM response
2025-08-03 21:45:14 - autocompose.agents.planning - ERROR - replan_local:580 - Local replanning failed: Failed to extract valid instruction from LLM response
2025-08-03 21:45:14 - autocompose.agents.controller - WARNING - execute_rollback:240 - Rollback failed for strategy: local
2025-08-03 21:45:14 - autocompose.agents.restoration - ERROR - _execute_plan_with_planning_chain:374 - Rollback failed, continuing with original result
2025-08-03 21:45:15 - autocompose.agents.restoration - INFO - _execute_plan_with_planning_chain:407 - Planning Chain execution complete:
2025-08-03 21:45:15 - autocompose.agents.restoration - INFO - _execute_plan_with_planning_chain:408 -   Initial score: 0.584, Final score: 0.584
2025-08-03 21:45:15 - autocompose.agents.restoration - INFO - _execute_plan_with_planning_chain:409 -   Total improvement: 0.000
2025-08-03 21:45:15 - autocompose.agents.restoration - INFO - _execute_plan_with_planning_chain:410 -   Steps executed: 1, Rollbacks: {'local': 1, 'global': 0}
2025-08-03 21:45:15 - performance - INFO - end_timer:58 - Operation 'plan execution' completed in 0.856s
2025-08-03 21:45:15 - autocompose.main - INFO - __exit__:239 - Completed: Plan Execution (0.856s)
2025-08-03 21:45:15 - autocompose.main - INFO - process_image:173 - Final aesthetic score: 0.584 (Improvement: 0.000)
2025-08-03 21:45:15 - autocompose.main - INFO - process_image:177 - Success rate: 0.0%
2025-08-03 21:45:15 - autocompose.main - INFO - _save_output_image:239 - Saved result to: output.png
2025-08-03 21:45:15 - autocompose.main - INFO - _save_intermediate_results:298 - Saved intermediate results to output_*
