
import logging  # 导入日志模块，用于记录控制器运行过程中的信息
import time  # 导入时间模块，用于记录时间戳
from typing import Dict, List, Any, Optional, Tuple  # 导入类型注解，提高代码可读性
from dataclasses import dataclass, field  # 导入数据类装饰器，用于创建数据结构
from enum import Enum  # 导入枚举类，用于定义回滚策略
from PIL import Image  # 导入PIL图像处理库

# 导入相关模块和类
from .planning import PlanningFeedback, AdjustmentPlan, EditingInstruction, PlanningContext
from .perception import PerceptionResult
from .unified_types import ControllerDecision

# 避免循环导入，使用类型注解
from typing import TYPE_CHECKING
if TYPE_CHECKING:
    from .unified_types import OperationResult

logger = logging.getLogger(__name__)  # 创建当前模块的日志记录器


class RollbackStrategy(Enum):
    """回滚策略枚举 - 定义了控制器可以采取的不同回滚策略"""
    CONTINUE = "continue"           # 继续执行，不回滚 - 当前步骤执行良好，继续下一步
    LOCAL_ROLLBACK = "local"        # 局部回滚，重做当前步骤 - 只重新规划失败的当前步骤
    GLOBAL_ROLLBACK = "global"      # 全局回滚，重新规划整个计划 - 重新规划所有剩余步骤
    ABORT = "abort"                 # 中止执行 - 当回滚次数超限或遇到严重错误时中止


# ControllerDecision 已移至 unified_types.py，避免重复定义


class PlanningChainController:
    """Planning Chain 控制器 - 核心控制器类，负责监控和控制规划链的执行"""
    
    def __init__(self, profile, planner = None):
        """
        初始化控制器
        
        Args:
            profile: 系统配置 - 包含控制器运行所需的各种参数配置
            planner: Planning Chain 规划器实例 - 用于执行重新规划操作
        """
        self.profile = profile  # 保存系统配置对象
        self.planner = planner  # 保存规划器实例，用于后续的重新规划操作
        
        # 配置参数 - 从profile中读取控制器的各种阈值参数
        # 回滚阈值：当改进值低于此值时考虑回滚
        self.rollback_threshold = getattr(profile.profile, 'rollback_threshold', 0.0) if profile.profile else 0.0
        # 最大局部回滚次数：防止局部回滚无限循环
        self.max_local_rollbacks = getattr(profile.profile, 'max_local_rollbacks', 2) if profile.profile else 2
        # 最大全局回滚次数：防止全局回滚无限循环
        self.max_global_rollbacks = getattr(profile.profile, 'max_global_rollbacks', 1) if profile.profile else 1
        # 最小改进阈值：低于此值认为是严重退化
        self.min_improvement_threshold = getattr(profile.profile, 'min_improvement_threshold', -0.1) if profile.profile else -0.1
        
        # 执行状态跟踪 - 维护控制器的内部状态
        self.execution_history = []  # 记录所有执行步骤的历史，用于分析和调试
        self.rollback_counts = {"local": 0, "global": 0}  # 记录各种回滚操作的次数
        self.current_plan = None  # 当前正在执行的计划
        self.original_perception = None  # 原始的感知结果，用于全局重新规划
        
        # 记录初始化信息到日志
        logger.info(f"Planning Chain Controller initialized:")
        logger.info(f"  - Rollback threshold: {self.rollback_threshold}")
        logger.info(f"  - Max local rollbacks: {self.max_local_rollbacks}")
        logger.info(f"  - Max global rollbacks: {self.max_global_rollbacks}")
    
    def start_execution(self, plan: AdjustmentPlan, perception_result: PerceptionResult):
        """开始执行计划 - 初始化执行状态，为新的计划执行做准备"""
        self.current_plan = plan  # 设置当前要执行的计划
        self.original_perception = perception_result  # 保存原始感知结果，用于后续的全局重新规划
        self.execution_history = []  # 清空执行历史，开始新的执行记录
        self.rollback_counts = {"local": 0, "global": 0}  # 重置回滚计数器
        logger.info(f"Started execution of plan with {len(plan.instructions)} steps")  # 记录开始执行的计划信息
    
    def evaluate_step_result(self, step_result: 'OperationResult',
                           step_instruction: EditingInstruction) -> ControllerDecision:
        """
        评估单个步骤的执行结果并决定下一步行动
        这是控制器的核心决策函数，根据执行结果决定是否需要回滚
        
        Args:
            step_result: 步骤执行结果 - 包含成功状态、改进值、错误信息等
            step_instruction: 执行的指令 - 当前执行的编辑指令
            
        Returns:
            控制器决策 - 包含策略、原因、置信度等信息
        """
        # 记录执行历史 - 将当前步骤的执行记录添加到历史中
        self.execution_history.append({
            'instruction': step_instruction,  # 执行的指令
            'result': step_result,  # 执行结果
            'timestamp': time.time()  # 执行时间戳
        })
        
        # 评估结果 - 从执行结果中提取关键指标
        improvement = step_result.improvement  # 改进值：正值表示改进，负值表示退化
        success = step_result.success  # 执行成功状态：True表示技术上执行成功
        
        # 记录评估信息到日志
        logger.info(f"Evaluating step: '{step_instruction.instruction[:50]}...'")  # 截取指令前50个字符记录
        logger.info(f"  Success: {success}, Improvement: {improvement:.3f}")  # 记录成功状态和改进值
        
        # 决策逻辑 - 根据执行结果的不同情况采取不同的决策
        if not success:
            # 执行失败 - 技术上无法执行，需要回滚
            return self._handle_execution_failure(step_result, step_instruction)
        elif improvement < self.min_improvement_threshold:
            # 严重退化 - 改进值低于最小阈值，可能需要全局回滚
            return self._handle_severe_degradation(step_result, step_instruction)
        elif improvement < self.rollback_threshold:
            # 轻微退化或无改进 - 改进值低于回滚阈值，考虑局部回滚
            return self._handle_minor_degradation(step_result, step_instruction)
        else:
            # 成功改进 - 改进值达到期望，继续执行下一步
            return ControllerDecision(
                strategy=RollbackStrategy.CONTINUE.value,  # 策略：继续执行
                reason=f"Good improvement: {improvement:.3f}"  # 原因：良好的改进
            )
    
    def _handle_execution_failure(self, step_result: 'OperationResult',
                                step_instruction: EditingInstruction) -> ControllerDecision:
        """处理执行失败 - 当步骤技术上执行失败时的处理策略"""
        if self.rollback_counts["local"] < self.max_local_rollbacks:
            # 如果局部回滚次数未达上限，优先尝试局部回滚
            return ControllerDecision(
                strategy=RollbackStrategy.LOCAL_ROLLBACK.value,  # 策略：局部回滚
                reason=f"Execution failed: {step_result.message}"  # 原因：包含具体错误信息
            )
        elif self.rollback_counts["global"] < self.max_global_rollbacks:
            # 如果局部回滚已达上限但全局回滚未达上限，尝试全局回滚
            return ControllerDecision(
                strategy=RollbackStrategy.GLOBAL_ROLLBACK.value,  # 策略：全局回滚
                reason="Too many local rollbacks, trying global replan"  # 原因：局部回滚次数过多
            )
        else:
            # 如果所有回滚次数都已达上限，中止执行
            return ControllerDecision(
                strategy=RollbackStrategy.ABORT.value,  # 策略：中止执行
                reason="Exceeded maximum rollback attempts"  # 原因：超过最大回滚尝试次数
            )
    
    def _handle_severe_degradation(self, step_result: 'OperationResult',
                                 step_instruction: EditingInstruction) -> ControllerDecision:
        """处理严重退化 - 当改进值严重低于预期时的处理策略"""
        improvement = step_result.improvement  # 获取改进值
        
        if self.rollback_counts["global"] < self.max_global_rollbacks:
            # 严重退化优先考虑全局回滚，因为可能是整体规划有问题
            return ControllerDecision(
                strategy=RollbackStrategy.GLOBAL_ROLLBACK.value,  # 策略：全局回滚
                reason=f"Severe degradation: {improvement:.3f}"  # 原因：包含具体退化值
            )
        elif self.rollback_counts["local"] < self.max_local_rollbacks:
            # 如果全局回滚已达上限，尝试局部回滚作为备选方案
            return ControllerDecision(
                strategy=RollbackStrategy.LOCAL_ROLLBACK.value,  # 策略：局部回滚
                reason=f"Severe degradation, trying local fix: {improvement:.3f}"  # 原因：尝试局部修复
            )
        else:
            # 如果所有回滚次数都已达上限，中止执行
            return ControllerDecision(
                strategy=RollbackStrategy.ABORT.value,  # 策略：中止执行
                reason="Severe degradation and no rollbacks left"  # 原因：严重退化且无回滚余量
            )
    
    def _handle_minor_degradation(self, step_result: 'OperationResult',
                                step_instruction: EditingInstruction) -> ControllerDecision:
        """处理轻微退化 - 当改进值略低于预期时的处理策略"""
        improvement = step_result.improvement  # 获取改进值
        
        if self.rollback_counts["local"] < self.max_local_rollbacks:
            # 轻微退化优先尝试局部回滚，成本较低
            return ControllerDecision(
                strategy=RollbackStrategy.LOCAL_ROLLBACK.value,  # 策略：局部回滚
                reason=f"Minor degradation: {improvement:.3f}"  # 原因：包含具体退化值
            )
        else:
            # 如果局部回滚次数已用完，接受轻微退化继续执行
            return ControllerDecision(
                strategy=RollbackStrategy.CONTINUE.value,  # 策略：继续执行
                reason=f"Accepting minor degradation: {improvement:.3f} (no rollbacks left)"  # 原因：接受轻微退化
            )
    
    def execute_rollback(self, decision: ControllerDecision,
                        failed_step: EditingInstruction,
                        step_result: 'OperationResult') -> Optional[AdjustmentPlan]:
        """
        执行回滚操作 - 根据控制器决策执行具体的回滚动作
        
        Args:
            decision: 控制器决策 - 包含回滚策略和原因
            failed_step: 失败的步骤 - 需要回滚的编辑指令
            step_result: 步骤执行结果 - 包含失败原因和评分信息
            
        Returns:
            新的计划（如果成功）或 None - 重新规划后的新计划
        """
        if not self.planner:
            # 检查是否有可用的规划器
            logger.error("No planner available for rollback")
            return None
        
        # 创建反馈信息 - 为重新规划提供必要的反馈信息
        feedback = PlanningFeedback(
            failed_step=failed_step,  # 失败的步骤
            failure_reason=decision.reason,  # 失败原因
            score_before=step_result.score_before,  # 执行前评分
            score_after=step_result.score_after,  # 执行后评分
            improvement=step_result.improvement  # 改进值
        )
        
        try:
            if decision.strategy == RollbackStrategy.LOCAL_ROLLBACK.value:
                # 局部回滚 - 只重新规划当前失败的步骤
                self.rollback_counts["local"] += 1  # 增加局部回滚计数
                logger.info(f"Executing local rollback (attempt {self.rollback_counts['local']})")
                
                # 使用明确的局部重规划方法，直接返回完整计划
                new_plan = self.planner.replan_local(feedback, self.current_plan)

                if new_plan:
                    self.current_plan = new_plan  # 更新当前计划
                    return new_plan  # 返回新计划
                    
            elif decision.strategy == RollbackStrategy.GLOBAL_ROLLBACK.value:
                # 全局回滚 - 重新规划整个剩余计划
                self.rollback_counts["global"] += 1  # 增加全局回滚计数
                logger.info(f"Executing global rollback (attempt {self.rollback_counts['global']})")
                
                # 使用明确的全局重规划方法
                new_plan = self.planner.replan_global(feedback, self.original_perception)
                
                if new_plan:
                    self.current_plan = new_plan  # 更新当前计划
                    return new_plan  # 返回新计划
            
            # 如果回滚失败，记录警告
            logger.warning(f"Rollback failed for strategy: {decision.strategy}")
            return None
            
        except Exception as e:
            # 捕获并记录回滚执行过程中的异常
            logger.error(f"Rollback execution failed: {e}")
            return None
    
    def should_continue(self) -> bool:
        """判断是否应该继续执行 - 基于回滚次数限制决定是否继续"""
        total_rollbacks = sum(self.rollback_counts.values())  # 计算总回滚次数
        max_total_rollbacks = self.max_local_rollbacks + self.max_global_rollbacks  # 计算最大允许回滚次数
        
        return total_rollbacks < max_total_rollbacks  # 返回是否还有回滚余量


    
    def get_execution_summary(self) -> Dict[str, Any]:
        """获取执行摘要 - 提供控制器执行过程的统计信息"""
        return {
            'total_steps': len(self.execution_history),  # 总执行步骤数
            'rollback_counts': self.rollback_counts.copy(),  # 回滚次数统计副本
            'success_rate': self._calculate_success_rate(),  # 执行成功率
            'average_improvement': self._calculate_average_improvement()  # 平均改进值
        }
    
    def _calculate_success_rate(self) -> float:
        """计算成功率 - 统计执行成功的步骤比例"""
        if not self.execution_history:
            # 如果没有执行历史，返回0
            return 0.0
        
        successful = sum(1 for entry in self.execution_history if entry['result'].success)  # 统计成功的步骤数
        return successful / len(self.execution_history)  # 计算成功率
    
    def _calculate_average_improvement(self) -> float:
        """计算平均改进值 - 统计所有步骤的平均改进程度"""
        if not self.execution_history:
            # 如果没有执行历史，返回0
            return 0.0
        
        improvements = [entry['result'].improvement for entry in self.execution_history]  # 提取所有改进值
        return sum(improvements) / len(improvements)  # 计算平均值
